package server.dao;

import server.DatabaseConnection;
import server.models.ExamPaper;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 试卷数据访问对象
 */
public class ExamPaperDAO {
    
    /**
     * 添加试卷信息
     * @param examPaper 试卷对象
     * @return 是否添加成功
     */
    public boolean addExamPaper(ExamPaper examPaper) {
        String sql = "INSERT INTO exam_paper (paper_name, paper_code, teacher_id, course_id, exam_time, file_name) " +
                     "VALUES (?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, examPaper.getPaperName());
            stmt.setString(2, examPaper.getPaperCode());
            stmt.setInt(3, examPaper.getTeacherId());
            stmt.setInt(4, examPaper.getCourseId());
            stmt.setTimestamp(5, examPaper.getExamTime());
            stmt.setString(6, examPaper.getFileName());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error adding exam paper: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 根据教师ID获取试卷列表
     * @param teacherId 教师ID
     * @return 试卷列表
     */
    public List<ExamPaper> getExamPapersByTeacherId(int teacherId) {
        List<ExamPaper> examPapers = new ArrayList<>();
        String sql = "SELECT ep.id, ep.paper_name, ep.paper_code, ep.teacher_id, ep.course_id, " +
                     "ep.exam_time, ep.file_name, ep.created_time, t.name as teacher_name, c.course_name " +
                     "FROM exam_paper ep " +
                     "LEFT JOIN teacher t ON ep.teacher_id = t.id " +
                     "LEFT JOIN course c ON ep.course_id = c.id " +
                     "WHERE ep.teacher_id = ? ORDER BY ep.created_time DESC";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, teacherId);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ExamPaper examPaper = new ExamPaper();
                    examPaper.setId(rs.getInt("id"));
                    examPaper.setPaperName(rs.getString("paper_name"));
                    examPaper.setPaperCode(rs.getString("paper_code"));
                    examPaper.setTeacherId(rs.getInt("teacher_id"));
                    examPaper.setCourseId(rs.getInt("course_id"));
                    examPaper.setExamTime(rs.getTimestamp("exam_time"));
                    examPaper.setFileName(rs.getString("file_name"));
                    examPaper.setCreatedTime(rs.getTimestamp("created_time"));
                    examPaper.setTeacherName(rs.getString("teacher_name"));
                    examPaper.setCourseName(rs.getString("course_name"));
                    examPapers.add(examPaper);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting exam papers by teacher id: " + e.getMessage());
        }
        
        return examPapers;
    }
    
    /**
     * 获取所有试卷信息（管理员查询用）
     * @return 试卷列表
     */
    public List<ExamPaper> getAllExamPapers() {
        List<ExamPaper> examPapers = new ArrayList<>();
        String sql = "SELECT ep.id, ep.paper_name, ep.paper_code, ep.teacher_id, ep.course_id, " +
                     "ep.exam_time, ep.file_name, ep.created_time, t.name as teacher_name, c.course_name " +
                     "FROM exam_paper ep " +
                     "LEFT JOIN teacher t ON ep.teacher_id = t.id " +
                     "LEFT JOIN course c ON ep.course_id = c.id " +
                     "ORDER BY ep.created_time DESC";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                ExamPaper examPaper = new ExamPaper();
                examPaper.setId(rs.getInt("id"));
                examPaper.setPaperName(rs.getString("paper_name"));
                examPaper.setPaperCode(rs.getString("paper_code"));
                examPaper.setTeacherId(rs.getInt("teacher_id"));
                examPaper.setCourseId(rs.getInt("course_id"));
                examPaper.setExamTime(rs.getTimestamp("exam_time"));
                examPaper.setFileName(rs.getString("file_name"));
                examPaper.setCreatedTime(rs.getTimestamp("created_time"));
                examPaper.setTeacherName(rs.getString("teacher_name"));
                examPaper.setCourseName(rs.getString("course_name"));
                examPapers.add(examPaper);
            }
        } catch (SQLException e) {
            System.err.println("Error getting all exam papers: " + e.getMessage());
        }
        
        return examPapers;
    }
    
    /**
     * 根据课程ID获取试卷列表
     * @param courseId 课程ID
     * @return 试卷列表
     */
    public List<ExamPaper> getExamPapersByCourseId(int courseId) {
        List<ExamPaper> examPapers = new ArrayList<>();
        String sql = "SELECT ep.id, ep.paper_name, ep.paper_code, ep.teacher_id, ep.course_id, " +
                     "ep.exam_time, ep.file_name, ep.created_time, t.name as teacher_name, c.course_name " +
                     "FROM exam_paper ep " +
                     "LEFT JOIN teacher t ON ep.teacher_id = t.id " +
                     "LEFT JOIN course c ON ep.course_id = c.id " +
                     "WHERE ep.course_id = ? ORDER BY ep.created_time DESC";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, courseId);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ExamPaper examPaper = new ExamPaper();
                    examPaper.setId(rs.getInt("id"));
                    examPaper.setPaperName(rs.getString("paper_name"));
                    examPaper.setPaperCode(rs.getString("paper_code"));
                    examPaper.setTeacherId(rs.getInt("teacher_id"));
                    examPaper.setCourseId(rs.getInt("course_id"));
                    examPaper.setExamTime(rs.getTimestamp("exam_time"));
                    examPaper.setFileName(rs.getString("file_name"));
                    examPaper.setCreatedTime(rs.getTimestamp("created_time"));
                    examPaper.setTeacherName(rs.getString("teacher_name"));
                    examPaper.setCourseName(rs.getString("course_name"));
                    examPapers.add(examPaper);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting exam papers by course id: " + e.getMessage());
        }
        
        return examPapers;
    }
    
    /**
     * 检查试卷编号是否已存在
     * @param paperCode 试卷编号
     * @return 是否存在
     */
    public boolean isPaperCodeExists(String paperCode) {
        String sql = "SELECT COUNT(*) FROM exam_paper WHERE paper_code = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, paperCode);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error checking paper code exists: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 根据ID获取试卷信息
     * @param id 试卷ID
     * @return 试卷对象
     */
    public ExamPaper getExamPaperById(int id) {
        String sql = "SELECT ep.id, ep.paper_name, ep.paper_code, ep.teacher_id, ep.course_id, " +
                     "ep.exam_time, ep.file_name, ep.created_time, t.name as teacher_name, c.course_name " +
                     "FROM exam_paper ep " +
                     "LEFT JOIN teacher t ON ep.teacher_id = t.id " +
                     "LEFT JOIN course c ON ep.course_id = c.id " +
                     "WHERE ep.id = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    ExamPaper examPaper = new ExamPaper();
                    examPaper.setId(rs.getInt("id"));
                    examPaper.setPaperName(rs.getString("paper_name"));
                    examPaper.setPaperCode(rs.getString("paper_code"));
                    examPaper.setTeacherId(rs.getInt("teacher_id"));
                    examPaper.setCourseId(rs.getInt("course_id"));
                    examPaper.setExamTime(rs.getTimestamp("exam_time"));
                    examPaper.setFileName(rs.getString("file_name"));
                    examPaper.setCreatedTime(rs.getTimestamp("created_time"));
                    examPaper.setTeacherName(rs.getString("teacher_name"));
                    examPaper.setCourseName(rs.getString("course_name"));
                    return examPaper;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting exam paper by id: " + e.getMessage());
        }
        
        return null;
    }
}

package client;

import common.Message;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import server.models.*;

import java.io.*;
import java.net.Socket;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Scanner;

/**
 * 客户端主程序
 */
public class Client {
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8888;
    
    private Socket socket;
    private ObjectOutputStream outputStream;
    private ObjectInputStream inputStream;
    private Scanner scanner;
    private Gson gson;
    private Teacher currentUser;
    
    public Client() {
        this.scanner = new Scanner(System.in);
        this.gson = new Gson();
    }
    
    /**
     * 连接服务器
     */
    public boolean connectToServer() {
        try {
            socket = new Socket(SERVER_HOST, SERVER_PORT);
            outputStream = new ObjectOutputStream(socket.getOutputStream());
            inputStream = new ObjectInputStream(socket.getInputStream());
            
            System.out.println("成功连接到服务器");
            return true;
        } catch (IOException e) {
            System.err.println("连接服务器失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 断开服务器连接
     */
    public void disconnect() {
        try {
            if (inputStream != null) inputStream.close();
            if (outputStream != null) outputStream.close();
            if (socket != null) socket.close();
            System.out.println("已断开服务器连接");
        } catch (IOException e) {
            System.err.println("断开连接时出错: " + e.getMessage());
        }
    }
    
    /**
     * 发送请求并接收响应
     */
    private Message sendRequest(Message request) {
        try {
            outputStream.writeObject(request);
            outputStream.flush();
            return (Message) inputStream.readObject();
        } catch (IOException | ClassNotFoundException e) {
            System.err.println("通信错误: " + e.getMessage());
            return new Message("ERROR", "", false, "通信失败");
        }
    }
    
    /**
     * 主菜单
     */
    public void showMainMenu() {
        while (true) {
            System.out.println("\n=== 学校试卷登记管理系统 ===");
            System.out.println("1. 教师注册");
            System.out.println("2. 用户登录");
            System.out.println("0. 退出系统");
            System.out.print("请选择操作: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    handleRegister();
                    break;
                case "2":
                    if (handleLogin()) {
                        if ("admin".equals(currentUser.getRole())) {
                            showAdminMenu();
                        } else {
                            showTeacherMenu();
                        }
                    }
                    break;
                case "0":
                    System.out.println("感谢使用，再见！");
                    return;
                default:
                    System.out.println("无效选择，请重新输入");
            }
        }
    }
    
    /**
     * 处理注册
     */
    private void handleRegister() {
        System.out.println("\n=== 教师注册 ===");
        
        // 获取学院列表
        Message collegeRequest = new Message("GET_COLLEGES", "");
        Message collegeResponse = sendRequest(collegeRequest);
        
        if (!collegeResponse.isSuccess()) {
            System.out.println("获取学院信息失败: " + collegeResponse.getMessage());
            return;
        }
        
        List<College> colleges = gson.fromJson(collegeResponse.getData(), 
                new TypeToken<List<College>>(){}.getType());
        
        if (colleges.isEmpty()) {
            System.out.println("暂无学院信息，请联系管理员添加学院");
            return;
        }
        
        // 显示学院列表
        System.out.println("可选学院:");
        for (College college : colleges) {
            System.out.println(college.getId() + ". " + college.getCollegeName() + 
                             " (" + college.getCollegeCode() + ")");
        }
        
        System.out.print("请输入姓名: ");
        String name = scanner.nextLine().trim();
        
        System.out.print("请选择学院ID: ");
        int collegeId;
        try {
            collegeId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("学院ID格式错误");
            return;
        }
        
        System.out.print("请输入手机号: ");
        String phone = scanner.nextLine().trim();
        
        System.out.print("请输入密码: ");
        String password = scanner.nextLine().trim();
        
        // 验证输入
        if (name.isEmpty() || phone.isEmpty() || password.isEmpty()) {
            System.out.println("所有字段都不能为空");
            return;
        }
        
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            System.out.println("手机号格式不正确");
            return;
        }
        
        // 创建教师对象
        Teacher teacher = new Teacher(name, collegeId, phone, password);
        String teacherJson = gson.toJson(teacher);
        
        // 发送注册请求
        Message request = new Message("REGISTER", teacherJson);
        Message response = sendRequest(request);
        
        if (response.isSuccess()) {
            System.out.println("注册成功！");
        } else {
            System.out.println("注册失败: " + response.getMessage());
        }
    }
    
    /**
     * 处理登录
     */
    private boolean handleLogin() {
        System.out.println("\n=== 用户登录 ===");
        
        System.out.print("请输入手机号: ");
        String phone = scanner.nextLine().trim();
        
        System.out.print("请输入密码: ");
        String password = scanner.nextLine().trim();
        
        if (phone.isEmpty() || password.isEmpty()) {
            System.out.println("手机号和密码不能为空");
            return false;
        }
        
        // 发送登录请求
        String credentials = phone + "," + password;
        Message request = new Message("LOGIN", credentials);
        Message response = sendRequest(request);
        
        if (response.isSuccess()) {
            currentUser = gson.fromJson(response.getData(), Teacher.class);
            System.out.println("登录成功！欢迎 " + currentUser.getName());
            return true;
        } else {
            System.out.println("登录失败: " + response.getMessage());
            return false;
        }
    }
    
    /**
     * 管理员菜单
     */
    private void showAdminMenu() {
        while (true) {
            System.out.println("\n=== 管理员菜单 ===");
            System.out.println("1. 学院信息管理");
            System.out.println("2. 课程信息管理");
            System.out.println("3. 查询试卷信息");
            System.out.println("0. 退出登录");
            System.out.print("请选择操作: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    showCollegeManagementMenu();
                    break;
                case "2":
                    showCourseManagementMenu();
                    break;
                case "3":
                    showExamPaperQuery();
                    break;
                case "0":
                    currentUser = null;
                    System.out.println("已退出登录");
                    return;
                default:
                    System.out.println("无效选择，请重新输入");
            }
        }
    }
    
    /**
     * 教师菜单
     */
    private void showTeacherMenu() {
        while (true) {
            System.out.println("\n=== 教师菜单 ===");
            System.out.println("1. 查看我的课程");
            System.out.println("2. 录入试卷信息");
            System.out.println("3. 查看我的试卷");
            System.out.println("0. 退出登录");
            System.out.print("请选择操作: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    showMyCourses();
                    break;
                case "2":
                    addExamPaper();
                    break;
                case "3":
                    showMyExamPapers();
                    break;
                case "0":
                    currentUser = null;
                    System.out.println("已退出登录");
                    return;
                default:
                    System.out.println("无效选择，请重新输入");
            }
        }
    }
    
    /**
     * 学院管理菜单
     */
    private void showCollegeManagementMenu() {
        while (true) {
            System.out.println("\n=== 学院信息管理 ===");
            System.out.println("1. 查看所有学院");
            System.out.println("2. 添加学院");
            System.out.println("3. 修改学院");
            System.out.println("4. 删除学院");
            System.out.println("0. 返回上级菜单");
            System.out.print("请选择操作: ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    showAllColleges();
                    break;
                case "2":
                    addCollege();
                    break;
                case "3":
                    updateCollege();
                    break;
                case "4":
                    deleteCollege();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("无效选择，请重新输入");
            }
        }
    }
    
    /**
     * 显示所有学院
     */
    private void showAllColleges() {
        Message request = new Message("GET_COLLEGES", "");
        Message response = sendRequest(request);
        
        if (response.isSuccess()) {
            List<College> colleges = gson.fromJson(response.getData(), 
                    new TypeToken<List<College>>(){}.getType());
            
            if (colleges.isEmpty()) {
                System.out.println("暂无学院信息");
            } else {
                System.out.println("\n学院列表:");
                System.out.println("ID\t学院名称\t\t学院编号");
                System.out.println("----------------------------------------");
                for (College college : colleges) {
                    System.out.printf("%d\t%s\t\t%s\n", 
                            college.getId(), college.getCollegeName(), college.getCollegeCode());
                }
            }
        } else {
            System.out.println("获取学院列表失败: " + response.getMessage());
        }
    }
    
    /**
     * 添加学院
     */
    private void addCollege() {
        System.out.println("\n=== 添加学院 ===");
        
        System.out.print("请输入学院名称: ");
        String collegeName = scanner.nextLine().trim();
        
        System.out.print("请输入学院编号: ");
        String collegeCode = scanner.nextLine().trim();
        
        if (collegeName.isEmpty() || collegeCode.isEmpty()) {
            System.out.println("学院名称和编号不能为空");
            return;
        }
        
        College college = new College(collegeName, collegeCode);
        String collegeJson = gson.toJson(college);
        
        Message request = new Message("ADD_COLLEGE", collegeJson);
        Message response = sendRequest(request);
        
        if (response.isSuccess()) {
            System.out.println("添加学院成功！");
        } else {
            System.out.println("添加学院失败: " + response.getMessage());
        }
    }
    
    /**
     * 修改学院
     */
    private void updateCollege() {
        // 先显示所有学院
        showAllColleges();
        
        System.out.println("\n=== 修改学院 ===");
        System.out.print("请输入要修改的学院ID: ");
        
        int collegeId;
        try {
            collegeId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("学院ID格式错误");
            return;
        }
        
        System.out.print("请输入新的学院名称: ");
        String collegeName = scanner.nextLine().trim();
        
        System.out.print("请输入新的学院编号: ");
        String collegeCode = scanner.nextLine().trim();
        
        if (collegeName.isEmpty() || collegeCode.isEmpty()) {
            System.out.println("学院名称和编号不能为空");
            return;
        }
        
        College college = new College(collegeId, collegeName, collegeCode);
        String collegeJson = gson.toJson(college);
        
        Message request = new Message("UPDATE_COLLEGE", collegeJson);
        Message response = sendRequest(request);
        
        if (response.isSuccess()) {
            System.out.println("修改学院成功！");
        } else {
            System.out.println("修改学院失败: " + response.getMessage());
        }
    }
    
    /**
     * 删除学院
     */
    private void deleteCollege() {
        // 先显示所有学院
        showAllColleges();
        
        System.out.println("\n=== 删除学院 ===");
        System.out.print("请输入要删除的学院ID: ");
        
        int collegeId;
        try {
            collegeId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("学院ID格式错误");
            return;
        }
        
        System.out.print("确认删除学院 (y/N): ");
        String confirm = scanner.nextLine().trim();
        
        if (!"y".equalsIgnoreCase(confirm)) {
            System.out.println("取消删除");
            return;
        }
        
        Message request = new Message("DELETE_COLLEGE", String.valueOf(collegeId));
        Message response = sendRequest(request);
        
        if (response.isSuccess()) {
            System.out.println("删除学院成功！");
        } else {
            System.out.println("删除学院失败: " + response.getMessage());
        }
    }
    
    /**
     * 课程管理菜单
     */
    private void showCourseManagementMenu() {
        while (true) {
            System.out.println("\n=== 课程信息管理 ===");
            System.out.println("1. 查看所有课程");
            System.out.println("2. 添加课程");
            System.out.println("3. 修改课程");
            System.out.println("4. 删除课程");
            System.out.println("5. 设置课程开课");
            System.out.println("0. 返回上级菜单");
            System.out.print("请选择操作: ");

            String choice = scanner.nextLine().trim();

            switch (choice) {
                case "1":
                    showAllCourses();
                    break;
                case "2":
                    addCourse();
                    break;
                case "3":
                    updateCourse();
                    break;
                case "4":
                    deleteCourse();
                    break;
                case "5":
                    activateCourse();
                    break;
                case "0":
                    return;
                default:
                    System.out.println("无效选择，请重新输入");
            }
        }
    }

    /**
     * 显示所有课程
     */
    private void showAllCourses() {
        Message request = new Message("GET_COURSES", "");
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            List<Course> courses = gson.fromJson(response.getData(),
                    new TypeToken<List<Course>>(){}.getType());

            if (courses.isEmpty()) {
                System.out.println("暂无课程信息");
            } else {
                System.out.println("\n课程列表:");
                System.out.println("ID\t课程名称\t\t任课教师\t\t考试时间\t\t\t状态");
                System.out.println("------------------------------------------------------------------------");
                for (Course course : courses) {
                    String examTimeStr = course.getExamTime() != null ?
                            course.getExamTime().toString() : "未设定";
                    String statusStr = "draft".equals(course.getStatus()) ? "草稿" : "开课";
                    System.out.printf("%d\t%s\t\t%s\t\t%s\t%s\n",
                            course.getId(), course.getCourseName(),
                            course.getTeacherName(), examTimeStr, statusStr);
                }
            }
        } else {
            System.out.println("获取课程列表失败: " + response.getMessage());
        }
    }

    /**
     * 添加课程
     */
    private void addCourse() {
        System.out.println("\n=== 添加课程 ===");

        // 获取教师列表
        Message teacherRequest = new Message("GET_TEACHERS", "");
        Message teacherResponse = sendRequest(teacherRequest);

        if (!teacherResponse.isSuccess()) {
            System.out.println("获取教师信息失败: " + teacherResponse.getMessage());
            return;
        }

        List<Teacher> teachers = gson.fromJson(teacherResponse.getData(),
                new TypeToken<List<Teacher>>(){}.getType());

        if (teachers.isEmpty()) {
            System.out.println("暂无教师信息");
            return;
        }

        // 显示教师列表
        System.out.println("可选教师:");
        for (Teacher teacher : teachers) {
            System.out.println(teacher.getId() + ". " + teacher.getName() +
                             " (" + teacher.getCollegeName() + ")");
        }

        System.out.print("请输入课程名称: ");
        String courseName = scanner.nextLine().trim();

        System.out.print("请选择任课教师ID: ");
        int teacherId;
        try {
            teacherId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("教师ID格式错误");
            return;
        }

        System.out.print("请输入考试时间 (格式: yyyy-MM-dd HH:mm:ss，可选): ");
        String examTimeStr = scanner.nextLine().trim();

        if (courseName.isEmpty()) {
            System.out.println("课程名称不能为空");
            return;
        }

        // 创建课程对象
        Course course = new Course();
        course.setCourseName(courseName);
        course.setTeacherId(teacherId);
        course.setStatus("draft");

        if (!examTimeStr.isEmpty()) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = sdf.parse(examTimeStr);
                course.setExamTime(new Timestamp(date.getTime()));
            } catch (ParseException e) {
                System.out.println("考试时间格式错误，将不设置考试时间");
            }
        }

        String courseJson = gson.toJson(course);

        Message request = new Message("ADD_COURSE", courseJson);
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            System.out.println("添加课程成功！");
        } else {
            System.out.println("添加课程失败: " + response.getMessage());
        }
    }

    /**
     * 修改课程
     */
    private void updateCourse() {
        // 先显示所有课程
        showAllCourses();

        System.out.println("\n=== 修改课程 ===");
        System.out.print("请输入要修改的课程ID: ");

        int courseId;
        try {
            courseId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("课程ID格式错误");
            return;
        }

        // 获取教师列表
        Message teacherRequest = new Message("GET_TEACHERS", "");
        Message teacherResponse = sendRequest(teacherRequest);

        if (!teacherResponse.isSuccess()) {
            System.out.println("获取教师信息失败: " + teacherResponse.getMessage());
            return;
        }

        List<Teacher> teachers = gson.fromJson(teacherResponse.getData(),
                new TypeToken<List<Teacher>>(){}.getType());

        // 显示教师列表
        System.out.println("可选教师:");
        for (Teacher teacher : teachers) {
            System.out.println(teacher.getId() + ". " + teacher.getName() +
                             " (" + teacher.getCollegeName() + ")");
        }

        System.out.print("请输入新的课程名称: ");
        String courseName = scanner.nextLine().trim();

        System.out.print("请选择新的任课教师ID: ");
        int teacherId;
        try {
            teacherId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("教师ID格式错误");
            return;
        }

        System.out.print("请输入新的考试时间 (格式: yyyy-MM-dd HH:mm:ss，可选): ");
        String examTimeStr = scanner.nextLine().trim();

        if (courseName.isEmpty()) {
            System.out.println("课程名称不能为空");
            return;
        }

        // 创建课程对象
        Course course = new Course();
        course.setId(courseId);
        course.setCourseName(courseName);
        course.setTeacherId(teacherId);

        if (!examTimeStr.isEmpty()) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = sdf.parse(examTimeStr);
                course.setExamTime(new Timestamp(date.getTime()));
            } catch (ParseException e) {
                System.out.println("考试时间格式错误，将不设置考试时间");
            }
        }

        String courseJson = gson.toJson(course);

        Message request = new Message("UPDATE_COURSE", courseJson);
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            System.out.println("修改课程成功！");
        } else {
            System.out.println("修改课程失败: " + response.getMessage());
        }
    }

    /**
     * 删除课程
     */
    private void deleteCourse() {
        // 先显示所有课程
        showAllCourses();

        System.out.println("\n=== 删除课程 ===");
        System.out.print("请输入要删除的课程ID: ");

        int courseId;
        try {
            courseId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("课程ID格式错误");
            return;
        }

        System.out.print("确认删除课程 (y/N): ");
        String confirm = scanner.nextLine().trim();

        if (!"y".equalsIgnoreCase(confirm)) {
            System.out.println("取消删除");
            return;
        }

        Message request = new Message("DELETE_COURSE", String.valueOf(courseId));
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            System.out.println("删除课程成功！");
        } else {
            System.out.println("删除课程失败: " + response.getMessage());
        }
    }

    /**
     * 设置课程开课
     */
    private void activateCourse() {
        // 先显示所有课程
        showAllCourses();

        System.out.println("\n=== 设置课程开课 ===");
        System.out.print("请输入要开课的课程ID: ");

        int courseId;
        try {
            courseId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("课程ID格式错误");
            return;
        }

        System.out.print("确认设置课程为开课状态 (y/N): ");
        String confirm = scanner.nextLine().trim();

        if (!"y".equalsIgnoreCase(confirm)) {
            System.out.println("取消操作");
            return;
        }

        Message request = new Message("ACTIVATE_COURSE", String.valueOf(courseId));
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            System.out.println("课程开课设置成功！");
        } else {
            System.out.println("课程开课设置失败: " + response.getMessage());
        }
    }

    /**
     * 查看我的课程
     */
    private void showMyCourses() {
        Message request = new Message("GET_COURSES_BY_TEACHER", String.valueOf(currentUser.getId()));
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            List<Course> courses = gson.fromJson(response.getData(),
                    new TypeToken<List<Course>>(){}.getType());

            if (courses.isEmpty()) {
                System.out.println("您暂无教学课程");
            } else {
                System.out.println("\n我的课程:");
                System.out.println("ID\t课程名称\t\t考试时间");
                System.out.println("----------------------------------------");
                for (Course course : courses) {
                    String examTimeStr = course.getExamTime() != null ?
                            course.getExamTime().toString() : "未设定";
                    System.out.printf("%d\t%s\t\t%s\n",
                            course.getId(), course.getCourseName(), examTimeStr);
                }
            }
        } else {
            System.out.println("获取课程列表失败: " + response.getMessage());
        }
    }

    /**
     * 添加试卷信息
     */
    private void addExamPaper() {
        // 先获取我的课程
        Message courseRequest = new Message("GET_COURSES_BY_TEACHER", String.valueOf(currentUser.getId()));
        Message courseResponse = sendRequest(courseRequest);

        if (!courseResponse.isSuccess()) {
            System.out.println("获取课程信息失败: " + courseResponse.getMessage());
            return;
        }

        List<Course> courses = gson.fromJson(courseResponse.getData(),
                new TypeToken<List<Course>>(){}.getType());

        if (courses.isEmpty()) {
            System.out.println("您暂无教学课程，无法录入试卷");
            return;
        }

        System.out.println("\n=== 录入试卷信息 ===");

        // 显示课程列表
        System.out.println("您的课程:");
        for (Course course : courses) {
            System.out.println(course.getId() + ". " + course.getCourseName());
        }

        System.out.print("请选择课程ID: ");
        int courseId;
        try {
            courseId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("课程ID格式错误");
            return;
        }

        System.out.print("请输入试卷名称: ");
        String paperName = scanner.nextLine().trim();

        System.out.print("请输入试卷编号: ");
        String paperCode = scanner.nextLine().trim();

        System.out.print("请输入考试时间 (格式: yyyy-MM-dd HH:mm:ss): ");
        String examTimeStr = scanner.nextLine().trim();

        System.out.print("请输入试卷文件路径 (用于上传): ");
        String filePath = scanner.nextLine().trim();

        if (paperName.isEmpty() || paperCode.isEmpty() || examTimeStr.isEmpty()) {
            System.out.println("试卷名称、编号和考试时间不能为空");
            return;
        }

        // 解析考试时间
        Timestamp examTime;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = sdf.parse(examTimeStr);
            examTime = new Timestamp(date.getTime());
        } catch (ParseException e) {
            System.out.println("考试时间格式错误");
            return;
        }

        // 上传文件
        String fileName = null;
        if (!filePath.isEmpty()) {
            File file = new File(filePath);
            if (file.exists()) {
                String uploadData = filePath + "|" + file.getName();
                Message uploadRequest = new Message("UPLOAD_PAPER", uploadData);
                Message uploadResponse = sendRequest(uploadRequest);

                if (uploadResponse.isSuccess()) {
                    fileName = uploadResponse.getData();
                    System.out.println("文件上传成功");
                } else {
                    System.out.println("文件上传失败: " + uploadResponse.getMessage());
                    return;
                }
            } else {
                System.out.println("文件不存在: " + filePath);
                return;
            }
        }

        // 创建试卷对象
        ExamPaper examPaper = new ExamPaper();
        examPaper.setPaperName(paperName);
        examPaper.setPaperCode(paperCode);
        examPaper.setTeacherId(currentUser.getId());
        examPaper.setCourseId(courseId);
        examPaper.setExamTime(examTime);
        examPaper.setFileName(fileName);

        String examPaperJson = gson.toJson(examPaper);

        Message request = new Message("ADD_EXAM_PAPER", examPaperJson);
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            System.out.println("录入试卷信息成功！");
        } else {
            System.out.println("录入试卷信息失败: " + response.getMessage());
        }
    }

    /**
     * 查看我的试卷
     */
    private void showMyExamPapers() {
        Message request = new Message("GET_EXAM_PAPERS_BY_TEACHER", String.valueOf(currentUser.getId()));
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            List<ExamPaper> examPapers = gson.fromJson(response.getData(),
                    new TypeToken<List<ExamPaper>>(){}.getType());

            if (examPapers.isEmpty()) {
                System.out.println("您暂无试卷信息");
            } else {
                System.out.println("\n我的试卷:");
                System.out.println("ID\t试卷名称\t\t试卷编号\t\t课程名称\t\t考试时间");
                System.out.println("------------------------------------------------------------------------");
                for (ExamPaper paper : examPapers) {
                    System.out.printf("%d\t%s\t\t%s\t\t%s\t\t%s\n",
                            paper.getId(), paper.getPaperName(), paper.getPaperCode(),
                            paper.getCourseName(), paper.getExamTime().toString());
                }
            }
        } else {
            System.out.println("获取试卷列表失败: " + response.getMessage());
        }
    }

    /**
     * 试卷查询（管理员功能）
     */
    private void showExamPaperQuery() {
        Message request = new Message("GET_ALL_EXAM_PAPERS", "");
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            List<ExamPaper> examPapers = gson.fromJson(response.getData(),
                    new TypeToken<List<ExamPaper>>(){}.getType());

            if (examPapers.isEmpty()) {
                System.out.println("暂无试卷信息");
            } else {
                System.out.println("\n所有试卷信息:");
                System.out.println("ID\t试卷名称\t\t试卷编号\t\t出题教师\t\t课程名称\t\t考试时间\t\t\t文件名");
                System.out.println("------------------------------------------------------------------------------------------------");
                for (ExamPaper paper : examPapers) {
                    String fileName = paper.getFileName() != null ? paper.getFileName() : "无";
                    System.out.printf("%d\t%s\t\t%s\t\t%s\t\t%s\t\t%s\t%s\n",
                            paper.getId(), paper.getPaperName(), paper.getPaperCode(),
                            paper.getTeacherName(), paper.getCourseName(),
                            paper.getExamTime().toString(), fileName);
                }

                // 提供下载功能
                System.out.print("\n是否要下载试卷？(y/N): ");
                String choice = scanner.nextLine().trim();
                if ("y".equalsIgnoreCase(choice)) {
                    downloadExamPaper(examPapers);
                }
            }
        } else {
            System.out.println("获取试卷列表失败: " + response.getMessage());
        }
    }

    /**
     * 下载试卷
     */
    private void downloadExamPaper(List<ExamPaper> examPapers) {
        System.out.print("请输入要下载的试卷ID: ");
        int paperId;
        try {
            paperId = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            System.out.println("试卷ID格式错误");
            return;
        }

        // 查找试卷
        ExamPaper selectedPaper = null;
        for (ExamPaper paper : examPapers) {
            if (paper.getId() == paperId) {
                selectedPaper = paper;
                break;
            }
        }

        if (selectedPaper == null) {
            System.out.println("未找到指定的试卷");
            return;
        }

        if (selectedPaper.getFileName() == null) {
            System.out.println("该试卷没有上传文件");
            return;
        }

        System.out.print("请输入下载后的文件名: ");
        String downloadFileName = scanner.nextLine().trim();
        if (downloadFileName.isEmpty()) {
            downloadFileName = selectedPaper.getPaperName() + ".pdf";
        }

        String downloadData = selectedPaper.getFileName() + "|" + downloadFileName;
        Message request = new Message("DOWNLOAD_PAPER", downloadData);
        Message response = sendRequest(request);

        if (response.isSuccess()) {
            System.out.println("试卷下载成功，保存路径: " + response.getData());
        } else {
            System.out.println("试卷下载失败: " + response.getMessage());
        }
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        Client client = new Client();

        if (client.connectToServer()) {
            try {
                client.showMainMenu();
            } finally {
                client.disconnect();
            }
        } else {
            System.out.println("无法连接到服务器，请检查服务器是否启动");
        }
    }
}

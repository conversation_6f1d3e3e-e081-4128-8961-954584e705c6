第一章 系统设计
1.1 系统概述

1.2 系统功能设计
比如整个系统的功能结构图如图2.1所示。
（说明：这里以《学生基本信息管理系统》为例说明。每个小组必须以实际完成系统的功能结构图替换此图！整体小组的系统功能结构图相同！

图2.1 系统总体功能架构
（因此每个同学必须结合自己完成的内容进行详细说明，由于小组成员分工不同，结合本人完成的任务进行详细说明。

1.2.1 子系统1的功能需求
……

1.2.2 子系统2的功能需求

第二章 系统实现及测试
3.1 系统实现
（说明：本部分选取自己的核心模块，使用文字或流程图描述其实现思路，同时说明实现的具体细节，包含关键代码和部分界面。其它代码放在附件之中。设计报告打印时，必须删除此段说明文字）
3.2 测试

每种测试必须设计测试用例进行测试。测试用例的写法如下表所示。

项目名称	xxx	模块名称	xxx
测试目的	xxx
测试环境	xxx
序号	操作描述	测试结果	测试人员	备注
1	xxx	     xxx	xxx     xxx
2	xxx	     xxx	xxx     xxx
3	xxx	     xxx	xxx     xxx
4	xxx	     xxx	xxx     xxx
5	xxx	     xxx	xxx     xxx
6	xxx	     xxx	xxx     xxx
7	xxx	     xxx	xxx     xxx

测试结论	xxx
测试建议	xxx

第三章 总结
（说明：不但要对本次设计所做工作及收获进行总结，还要对所设计系统的不足进行总结。设计报告打印时，必须删除此段说明文字）
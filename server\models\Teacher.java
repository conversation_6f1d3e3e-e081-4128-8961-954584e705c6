package server.models;

import java.io.Serializable;

/**
 * 教师实体类
 */
public class Teacher implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private int id;
    private String name;
    private int collegeId;
    private String phone;
    private String password;
    private String role;
    private String collegeName; // 用于显示学院名称
    
    public Teacher() {}
    
    public Teacher(String name, int collegeId, String phone, String password) {
        this.name = name;
        this.collegeId = collegeId;
        this.phone = phone;
        this.password = password;
        this.role = "teacher";
    }
    
    public Teacher(int id, String name, int collegeId, String phone, String password, String role) {
        this.id = id;
        this.name = name;
        this.collegeId = collegeId;
        this.phone = phone;
        this.password = password;
        this.role = role;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public int getCollegeId() {
        return collegeId;
    }
    
    public void setCollegeId(int collegeId) {
        this.collegeId = collegeId;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getCollegeName() {
        return collegeName;
    }
    
    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }
    
    @Override
    public String toString() {
        return "Teacher{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", collegeId=" + collegeId +
                ", phone='" + phone + '\'' +
                ", role='" + role + '\'' +
                ", collegeName='" + collegeName + '\'' +
                '}';
    }
}

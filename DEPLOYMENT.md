# 学校试卷登记管理系统部署指南

## 系统要求

### 软件环境
- **操作系统**: Windows 10/11 或 Windows Server
- **Java**: JDK 8 或更高版本
- **数据库**: MySQL 8.0 或更高版本
- **内存**: 至少 2GB RAM
- **磁盘空间**: 至少 1GB 可用空间

### 网络要求
- 服务器端口 8888 需要开放
- 客户端需要能够访问服务器的 8888 端口

## 详细部署步骤

### 第一步：安装Java环境

1. 下载并安装 JDK 8 或更高版本
2. 配置 JAVA_HOME 环境变量
3. 将 %JAVA_HOME%\bin 添加到 PATH 环境变量
4. 验证安装：打开命令提示符，输入 `java -version`

### 第二步：安装MySQL数据库

1. 下载并安装 MySQL 8.0
2. 记住安装时设置的 root 密码
3. 启动 MySQL 服务
4. 验证安装：使用 MySQL Workbench 或命令行连接数据库

### 第三步：下载依赖库

在项目的 `lib/` 目录下放置以下JAR文件：

1. **mysql-connector-java-8.0.33.jar**
   - 下载地址：https://dev.mysql.com/downloads/connector/j/
   - 选择 "Platform Independent" 版本
   - 解压后在 `mysql-connector-java-8.0.33.jar` 文件

2. **gson-2.10.1.jar**
   - 下载地址：https://github.com/google/gson/releases/tag/gson-parent-2.10.1
   - 下载 `gson-2.10.1.jar` 文件

### 第四步：配置数据库

1. 打开 MySQL 命令行或 MySQL Workbench
2. 执行 `database/schema.sql` 脚本：
   ```sql
   source /path/to/database/schema.sql
   ```
   或者复制脚本内容直接执行

3. 验证数据库创建：
   ```sql
   USE exam_management_system;
   SHOW TABLES;
   ```

4. 检查初始数据：
   ```sql
   SELECT * FROM teacher WHERE role = 'admin';
   ```

### 第五步：配置数据库连接

编辑 `server/DatabaseConnection.java` 文件，修改数据库连接参数：

```java
private static final String URL = "*********************************************************************************************************";
private static final String USERNAME = "root";
private static final String PASSWORD = "你的MySQL密码"; // 修改为实际密码
```

### 第六步：编译项目

1. 打开命令提示符，进入项目根目录
2. 运行编译脚本：
   ```cmd
   compile.bat
   ```
3. 确认编译成功，检查 `out/` 目录是否生成

### 第七步：测试数据库连接

运行数据库连接测试：
```cmd
test_database.bat
```

如果连接失败，请检查：
- MySQL 服务是否启动
- 数据库名称是否正确
- 用户名密码是否正确
- 防火墙设置

### 第八步：启动系统

1. **启动服务器**：
   ```cmd
   run_server.bat
   ```
   看到 "服务器启动成功，监听端口: 8888" 表示启动成功

2. **启动客户端**（新开命令提示符窗口）：
   ```cmd
   run_client.bat
   ```

## 测试系统功能

### 管理员登录测试
- 手机号：13800000000
- 密码：admin123

### 基本功能测试流程

1. **管理员功能测试**：
   - 登录管理员账户
   - 添加学院信息
   - 添加课程信息
   - 设置课程开课状态

2. **教师注册测试**：
   - 注册新教师账户
   - 选择已创建的学院

3. **教师功能测试**：
   - 登录教师账户
   - 查看分配的课程
   - 录入试卷信息

4. **试卷管理测试**：
   - 管理员查询试卷信息
   - 下载试卷文件

## 常见问题解决

### 1. 编译错误
- 检查 JDK 版本是否正确
- 确认依赖库文件是否存在且文件名正确
- 检查 JAVA_HOME 环境变量

### 2. 数据库连接失败
- 检查 MySQL 服务状态
- 验证数据库用户名密码
- 确认数据库名称正确
- 检查防火墙设置

### 3. 服务器启动失败
- 检查端口 8888 是否被占用
- 确认数据库连接正常
- 查看错误日志信息

### 4. 客户端连接失败
- 确认服务器已启动
- 检查网络连接
- 验证服务器IP和端口

### 5. 文件上传/下载失败
- 检查 `papers/` 和 `downloads/` 目录权限
- 确认文件路径正确
- 验证磁盘空间充足

## 生产环境部署建议

1. **安全设置**：
   - 修改默认管理员密码
   - 使用强密码策略
   - 配置数据库访问权限

2. **性能优化**：
   - 调整数据库连接池大小
   - 配置适当的JVM参数
   - 定期清理日志文件

3. **备份策略**：
   - 定期备份数据库
   - 备份试卷文件目录
   - 制定灾难恢复计划

4. **监控和维护**：
   - 监控系统资源使用情况
   - 定期检查日志文件
   - 及时更新安全补丁

## 技术支持

如遇到部署问题，请检查：
1. 系统日志信息
2. 数据库连接状态
3. 网络连接情况
4. 文件权限设置

确保按照本指南逐步操作，大多数问题都可以通过仔细检查配置来解决。

# 依赖库说明

本项目需要以下依赖库：

## 必需的JAR文件

1. **mysql-connector-java-8.0.33.jar** - MySQL JDBC驱动
   - 下载地址: https://dev.mysql.com/downloads/connector/j/
   - 用于连接MySQL数据库

2. **gson-2.10.1.jar** - Google JSON库
   - 下载地址: https://github.com/google/gson/releases
   - 用于JSON序列化和反序列化

## 下载和安装说明

1. 请将上述JAR文件下载并放置在 `lib/` 目录下
2. 确保文件名与上述列表一致
3. 运行编译脚本前请确保所有依赖都已就位

## 目录结构
```
lib/
├── mysql-connector-java-8.0.33.jar
├── gson-2.10.1.jar
└── README.md (本文件)
```

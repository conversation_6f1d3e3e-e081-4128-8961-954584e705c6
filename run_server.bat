@echo off
echo 启动学校试卷登记管理系统服务器...

REM 检查编译输出目录
if not exist "out" (
    echo 错误: 未找到编译输出目录，请先运行 compile.bat 编译项目
    pause
    exit /b 1
)

REM 设置类路径
set CLASSPATH=out;lib\mysql-connector-java-8.0.33.jar;lib\gson-2.10.1.jar

REM 创建必要的目录
if not exist "papers" mkdir papers
if not exist "downloads" mkdir downloads

echo 正在启动服务器...
echo 请确保MySQL数据库已启动并且已执行database\schema.sql脚本
echo 按Ctrl+C可以停止服务器
echo.

java -cp %CLASSPATH% server.Server

pause

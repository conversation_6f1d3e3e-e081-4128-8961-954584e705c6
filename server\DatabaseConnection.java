package server;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库连接管理类
 */
public class DatabaseConnection {
    private static final String URL = "*********************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "root";
    
    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            System.err.println("MySQL JDBC Driver not found!");
            e.printStackTrace();
        }
    }
    
    /**
     * 获取数据库连接
     * @return Connection对象
     * @throws SQLException SQL异常
     */
    public static Connection getConnection() throws SQLException {
        return DriverManager.getConnection(URL, USERNAME, PASSWORD);
    }
    
    /**
     * 关闭数据库连接
     * @param connection 数据库连接
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                System.err.println("Error closing database connection: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试数据库连接
     * @return 连接是否成功
     */
    public static boolean testConnection() {
        try (Connection connection = getConnection()) {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            System.err.println("Database connection test failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试主方法
     */
    public static void main(String[] args) {
        System.out.println("=== 数据库连接测试 ===");
        System.out.println("数据库URL: " + URL);
        System.out.println("用户名: " + USERNAME);

        if (testConnection()) {
            System.out.println("✓ 数据库连接成功！");
        } else {
            System.out.println("✗ 数据库连接失败！");
            System.out.println("请检查：");
            System.out.println("1. MySQL服务是否启动");
            System.out.println("2. 数据库是否存在");
            System.out.println("3. 用户名密码是否正确");
            System.out.println("4. JDBC驱动是否正确");
        }
    }
}

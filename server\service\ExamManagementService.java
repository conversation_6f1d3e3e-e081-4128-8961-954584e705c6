package server.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import server.dao.*;
import server.models.*;
import common.Message;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 试卷管理系统业务逻辑服务类
 */
public class ExamManagementService {
    private final TeacherDAO teacherDAO;
    private final CollegeDAO collegeDAO;
    private final CourseDAO courseDAO;
    private final ExamPaperDAO examPaperDAO;
    private final Gson gson;
    
    // 试卷文件存储目录
    private static final String PAPER_STORAGE_DIR = "papers/";
    private static final String DOWNLOAD_DIR = "downloads/";
    
    public ExamManagementService() {
        this.teacherDAO = new TeacherDAO();
        this.collegeDAO = new CollegeDAO();
        this.courseDAO = new CourseDAO();
        this.examPaperDAO = new ExamPaperDAO();
        this.gson = new Gson();
        
        // 创建必要的目录
        createDirectories();
    }
    
    private void createDirectories() {
        try {
            Files.createDirectories(Paths.get(PAPER_STORAGE_DIR));
            Files.createDirectories(Paths.get(DOWNLOAD_DIR));
        } catch (IOException e) {
            System.err.println("Error creating directories: " + e.getMessage());
        }
    }
    
    /**
     * 处理客户端请求
     * @param request 请求消息
     * @return 响应消息
     */
    public Message processRequest(Message request) {
        try {
            switch (request.getType()) {
                case "REGISTER":
                    return handleRegister(request.getData());
                case "LOGIN":
                    return handleLogin(request.getData());
                case "GET_COLLEGES":
                    return handleGetColleges();
                case "ADD_COLLEGE":
                    return handleAddCollege(request.getData());
                case "UPDATE_COLLEGE":
                    return handleUpdateCollege(request.getData());
                case "DELETE_COLLEGE":
                    return handleDeleteCollege(request.getData());
                case "GET_TEACHERS":
                    return handleGetTeachers();
                case "ADD_COURSE":
                    return handleAddCourse(request.getData());
                case "GET_COURSES":
                    return handleGetCourses();
                case "GET_COURSES_BY_TEACHER":
                    return handleGetCoursesByTeacher(request.getData());
                case "UPDATE_COURSE":
                    return handleUpdateCourse(request.getData());
                case "DELETE_COURSE":
                    return handleDeleteCourse(request.getData());
                case "ACTIVATE_COURSE":
                    return handleActivateCourse(request.getData());
                case "ADD_EXAM_PAPER":
                    return handleAddExamPaper(request.getData());
                case "GET_EXAM_PAPERS_BY_TEACHER":
                    return handleGetExamPapersByTeacher(request.getData());
                case "GET_ALL_EXAM_PAPERS":
                    return handleGetAllExamPapers();
                case "UPLOAD_PAPER":
                    return handleUploadPaper(request.getData());
                case "DOWNLOAD_PAPER":
                    return handleDownloadPaper(request.getData());
                default:
                    return new Message("ERROR", "", false, "未知的请求类型");
            }
        } catch (Exception e) {
            System.err.println("Error processing request: " + e.getMessage());
            e.printStackTrace();
            return new Message("ERROR", "", false, "服务器内部错误: " + e.getMessage());
        }
    }
    
    private Message handleRegister(String data) {
        try {
            Teacher teacher = gson.fromJson(data, Teacher.class);
            
            // 检查手机号是否已存在
            if (teacherDAO.isPhoneExists(teacher.getPhone())) {
                return new Message("REGISTER", "", false, "手机号已存在");
            }
            
            // 注册教师
            if (teacherDAO.registerTeacher(teacher)) {
                return new Message("REGISTER", "", true, "注册成功");
            } else {
                return new Message("REGISTER", "", false, "注册失败");
            }
        } catch (Exception e) {
            return new Message("REGISTER", "", false, "注册失败: " + e.getMessage());
        }
    }
    
    private Message handleLogin(String data) {
        try {
            String[] credentials = data.split(",");
            if (credentials.length != 2) {
                return new Message("LOGIN", "", false, "登录参数错误");
            }
            
            String phone = credentials[0];
            String password = credentials[1];
            
            Teacher teacher = teacherDAO.login(phone, password);
            if (teacher != null) {
                String teacherJson = gson.toJson(teacher);
                return new Message("LOGIN", teacherJson, true, "登录成功");
            } else {
                return new Message("LOGIN", "", false, "手机号或密码错误");
            }
        } catch (Exception e) {
            return new Message("LOGIN", "", false, "登录失败: " + e.getMessage());
        }
    }
    
    private Message handleGetColleges() {
        try {
            List<College> colleges = collegeDAO.getAllColleges();
            String collegesJson = gson.toJson(colleges);
            return new Message("GET_COLLEGES", collegesJson, true, "获取学院列表成功");
        } catch (Exception e) {
            return new Message("GET_COLLEGES", "", false, "获取学院列表失败: " + e.getMessage());
        }
    }
    
    private Message handleAddCollege(String data) {
        try {
            College college = gson.fromJson(data, College.class);
            
            // 检查学院编号是否已存在
            if (collegeDAO.isCollegeCodeExists(college.getCollegeCode(), 0)) {
                return new Message("ADD_COLLEGE", "", false, "学院编号已存在");
            }
            
            if (collegeDAO.addCollege(college)) {
                return new Message("ADD_COLLEGE", "", true, "添加学院成功");
            } else {
                return new Message("ADD_COLLEGE", "", false, "添加学院失败");
            }
        } catch (Exception e) {
            return new Message("ADD_COLLEGE", "", false, "添加学院失败: " + e.getMessage());
        }
    }
    
    private Message handleUpdateCollege(String data) {
        try {
            College college = gson.fromJson(data, College.class);
            
            // 检查学院编号是否已存在（排除当前学院）
            if (collegeDAO.isCollegeCodeExists(college.getCollegeCode(), college.getId())) {
                return new Message("UPDATE_COLLEGE", "", false, "学院编号已存在");
            }
            
            if (collegeDAO.updateCollege(college)) {
                return new Message("UPDATE_COLLEGE", "", true, "更新学院成功");
            } else {
                return new Message("UPDATE_COLLEGE", "", false, "更新学院失败");
            }
        } catch (Exception e) {
            return new Message("UPDATE_COLLEGE", "", false, "更新学院失败: " + e.getMessage());
        }
    }
    
    private Message handleDeleteCollege(String data) {
        try {
            int collegeId = Integer.parseInt(data);
            if (collegeDAO.deleteCollege(collegeId)) {
                return new Message("DELETE_COLLEGE", "", true, "删除学院成功");
            } else {
                return new Message("DELETE_COLLEGE", "", false, "删除学院失败");
            }
        } catch (Exception e) {
            return new Message("DELETE_COLLEGE", "", false, "删除学院失败: " + e.getMessage());
        }
    }
    
    private Message handleGetTeachers() {
        try {
            List<Teacher> teachers = teacherDAO.getAllTeachers();
            String teachersJson = gson.toJson(teachers);
            return new Message("GET_TEACHERS", teachersJson, true, "获取教师列表成功");
        } catch (Exception e) {
            return new Message("GET_TEACHERS", "", false, "获取教师列表失败: " + e.getMessage());
        }
    }
    
    private Message handleAddCourse(String data) {
        try {
            Course course = gson.fromJson(data, Course.class);
            if (courseDAO.addCourse(course)) {
                return new Message("ADD_COURSE", "", true, "添加课程成功");
            } else {
                return new Message("ADD_COURSE", "", false, "添加课程失败");
            }
        } catch (Exception e) {
            return new Message("ADD_COURSE", "", false, "添加课程失败: " + e.getMessage());
        }
    }
    
    private Message handleGetCourses() {
        try {
            List<Course> courses = courseDAO.getAllCourses();
            String coursesJson = gson.toJson(courses);
            return new Message("GET_COURSES", coursesJson, true, "获取课程列表成功");
        } catch (Exception e) {
            return new Message("GET_COURSES", "", false, "获取课程列表失败: " + e.getMessage());
        }
    }
    
    private Message handleGetCoursesByTeacher(String data) {
        try {
            int teacherId = Integer.parseInt(data);
            List<Course> courses = courseDAO.getCoursesByTeacherId(teacherId);
            String coursesJson = gson.toJson(courses);
            return new Message("GET_COURSES_BY_TEACHER", coursesJson, true, "获取教师课程列表成功");
        } catch (Exception e) {
            return new Message("GET_COURSES_BY_TEACHER", "", false, "获取教师课程列表失败: " + e.getMessage());
        }
    }
    
    private Message handleUpdateCourse(String data) {
        try {
            Course course = gson.fromJson(data, Course.class);
            if (courseDAO.updateCourse(course)) {
                return new Message("UPDATE_COURSE", "", true, "更新课程成功");
            } else {
                return new Message("UPDATE_COURSE", "", false, "更新课程失败或课程已开课");
            }
        } catch (Exception e) {
            return new Message("UPDATE_COURSE", "", false, "更新课程失败: " + e.getMessage());
        }
    }
    
    private Message handleDeleteCourse(String data) {
        try {
            int courseId = Integer.parseInt(data);
            if (courseDAO.deleteCourse(courseId)) {
                return new Message("DELETE_COURSE", "", true, "删除课程成功");
            } else {
                return new Message("DELETE_COURSE", "", false, "删除课程失败或课程已开课");
            }
        } catch (Exception e) {
            return new Message("DELETE_COURSE", "", false, "删除课程失败: " + e.getMessage());
        }
    }
    
    private Message handleActivateCourse(String data) {
        try {
            int courseId = Integer.parseInt(data);
            if (courseDAO.activateCourse(courseId)) {
                return new Message("ACTIVATE_COURSE", "", true, "课程开课成功");
            } else {
                return new Message("ACTIVATE_COURSE", "", false, "课程开课失败");
            }
        } catch (Exception e) {
            return new Message("ACTIVATE_COURSE", "", false, "课程开课失败: " + e.getMessage());
        }
    }
    
    private Message handleAddExamPaper(String data) {
        try {
            ExamPaper examPaper = gson.fromJson(data, ExamPaper.class);
            
            // 检查试卷编号是否已存在
            if (examPaperDAO.isPaperCodeExists(examPaper.getPaperCode())) {
                return new Message("ADD_EXAM_PAPER", "", false, "试卷编号已存在");
            }
            
            if (examPaperDAO.addExamPaper(examPaper)) {
                return new Message("ADD_EXAM_PAPER", "", true, "添加试卷成功");
            } else {
                return new Message("ADD_EXAM_PAPER", "", false, "添加试卷失败");
            }
        } catch (Exception e) {
            return new Message("ADD_EXAM_PAPER", "", false, "添加试卷失败: " + e.getMessage());
        }
    }
    
    private Message handleGetExamPapersByTeacher(String data) {
        try {
            int teacherId = Integer.parseInt(data);
            List<ExamPaper> examPapers = examPaperDAO.getExamPapersByTeacherId(teacherId);
            String examPapersJson = gson.toJson(examPapers);
            return new Message("GET_EXAM_PAPERS_BY_TEACHER", examPapersJson, true, "获取教师试卷列表成功");
        } catch (Exception e) {
            return new Message("GET_EXAM_PAPERS_BY_TEACHER", "", false, "获取教师试卷列表失败: " + e.getMessage());
        }
    }
    
    private Message handleGetAllExamPapers() {
        try {
            List<ExamPaper> examPapers = examPaperDAO.getAllExamPapers();
            String examPapersJson = gson.toJson(examPapers);
            return new Message("GET_ALL_EXAM_PAPERS", examPapersJson, true, "获取所有试卷列表成功");
        } catch (Exception e) {
            return new Message("GET_ALL_EXAM_PAPERS", "", false, "获取所有试卷列表失败: " + e.getMessage());
        }
    }
    
    private Message handleUploadPaper(String data) {
        try {
            String[] parts = data.split("\\|");
            if (parts.length != 2) {
                return new Message("UPLOAD_PAPER", "", false, "上传参数错误");
            }
            
            String sourceFilePath = parts[0];
            String originalFileName = parts[1];
            
            // 生成唯一文件名
            String filePrefix = "";
            String fileExtension = "";
            int lastDotIndex = originalFileName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                filePrefix = originalFileName.substring(0, lastDotIndex);
                fileExtension = originalFileName.substring(lastDotIndex);
            }
            
            String uniqueFileName = filePrefix + fileExtension;
            String targetFilePath = PAPER_STORAGE_DIR + uniqueFileName;
            
            // 复制文件
            Path sourcePath = Paths.get(sourceFilePath);
            Path targetPath = Paths.get(targetFilePath);
            
            if (Files.exists(sourcePath)) {
                Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                return new Message("UPLOAD_PAPER", uniqueFileName, true, "文件上传成功");
            } else {
                return new Message("UPLOAD_PAPER", "", false, "源文件不存在");
            }
        } catch (Exception e) {
            return new Message("UPLOAD_PAPER", "", false, "文件上传失败: " + e.getMessage());
        }
    }
    
    private Message handleDownloadPaper(String data) {
        try {
            String[] parts = data.split("\\|");
            if (parts.length != 2) {
                return new Message("DOWNLOAD_PAPER", "", false, "下载参数错误");
            }
            
            String fileName = parts[0];
            String targetFileName = parts[1];
            
            String sourceFilePath = PAPER_STORAGE_DIR + fileName;
            String targetFilePath = DOWNLOAD_DIR + targetFileName;
            
            Path sourcePath = Paths.get(sourceFilePath);
            Path targetPath = Paths.get(targetFilePath);
            
            if (Files.exists(sourcePath)) {
                Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                return new Message("DOWNLOAD_PAPER", targetFilePath, true, "文件下载成功");
            } else {
                return new Message("DOWNLOAD_PAPER", "", false, "试卷文件不存在");
            }
        } catch (Exception e) {
            return new Message("DOWNLOAD_PAPER", "", false, "文件下载失败: " + e.getMessage());
        }
    }
}

package server.models;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 课程实体类
 */
public class Course implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private int id;
    private String courseName;
    private int teacherId;
    private Timestamp examTime;
    private String status;
    private String teacherName; // 用于显示教师姓名
    
    public Course() {}
    
    public Course(String courseName, int teacherId, Timestamp examTime) {
        this.courseName = courseName;
        this.teacherId = teacherId;
        this.examTime = examTime;
        this.status = "draft";
    }
    
    public Course(int id, String courseName, int teacherId, Timestamp examTime, String status) {
        this.id = id;
        this.courseName = courseName;
        this.teacherId = teacherId;
        this.examTime = examTime;
        this.status = status;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }
    
    public int getTeacherId() {
        return teacherId;
    }
    
    public void setTeacherId(int teacherId) {
        this.teacherId = teacherId;
    }
    
    public Timestamp getExamTime() {
        return examTime;
    }
    
    public void setExamTime(Timestamp examTime) {
        this.examTime = examTime;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getTeacherName() {
        return teacherName;
    }
    
    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }
    
    @Override
    public String toString() {
        return "Course{" +
                "id=" + id +
                ", courseName='" + courseName + '\'' +
                ", teacherId=" + teacherId +
                ", examTime=" + examTime +
                ", status='" + status + '\'' +
                ", teacherName='" + teacherName + '\'' +
                '}';
    }
}

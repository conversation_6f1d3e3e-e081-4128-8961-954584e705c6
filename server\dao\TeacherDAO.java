package server.dao;

import server.DatabaseConnection;
import server.models.Teacher;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 教师数据访问对象
 */
public class TeacherDAO {
    
    /**
     * 教师注册
     * @param teacher 教师对象
     * @return 是否注册成功
     */
    public boolean registerTeacher(Teacher teacher) {
        String sql = "INSERT INTO teacher (name, college_id, phone, password, role) VALUES (?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, teacher.getName());
            stmt.setInt(2, teacher.getCollegeId());
            stmt.setString(3, teacher.getPhone());
            stmt.setString(4, teacher.getPassword());
            stmt.setString(5, teacher.getRole());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error registering teacher: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 教师登录验证
     * @param phone 手机号
     * @param password 密码
     * @return 教师对象，登录失败返回null
     */
    public Teacher login(String phone, String password) {
        String sql = "SELECT t.id, t.name, t.college_id, t.phone, t.password, t.role, c.college_name " +
                     "FROM teacher t LEFT JOIN college c ON t.college_id = c.id " +
                     "WHERE t.phone = ? AND t.password = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, phone);
            stmt.setString(2, password);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Teacher teacher = new Teacher();
                    teacher.setId(rs.getInt("id"));
                    teacher.setName(rs.getString("name"));
                    teacher.setCollegeId(rs.getInt("college_id"));
                    teacher.setPhone(rs.getString("phone"));
                    teacher.setPassword(rs.getString("password"));
                    teacher.setRole(rs.getString("role"));
                    teacher.setCollegeName(rs.getString("college_name"));
                    return teacher;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error during teacher login: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 检查手机号是否已存在
     * @param phone 手机号
     * @return 是否存在
     */
    public boolean isPhoneExists(String phone) {
        String sql = "SELECT COUNT(*) FROM teacher WHERE phone = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, phone);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error checking phone exists: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 获取所有教师信息
     * @return 教师列表
     */
    public List<Teacher> getAllTeachers() {
        List<Teacher> teachers = new ArrayList<>();
        String sql = "SELECT t.id, t.name, t.college_id, t.phone, t.password, t.role, c.college_name " +
                     "FROM teacher t LEFT JOIN college c ON t.college_id = c.id " +
                     "WHERE t.role = 'teacher' ORDER BY t.id";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                Teacher teacher = new Teacher();
                teacher.setId(rs.getInt("id"));
                teacher.setName(rs.getString("name"));
                teacher.setCollegeId(rs.getInt("college_id"));
                teacher.setPhone(rs.getString("phone"));
                teacher.setPassword(rs.getString("password"));
                teacher.setRole(rs.getString("role"));
                teacher.setCollegeName(rs.getString("college_name"));
                teachers.add(teacher);
            }
        } catch (SQLException e) {
            System.err.println("Error getting all teachers: " + e.getMessage());
        }
        
        return teachers;
    }
    
    /**
     * 根据ID获取教师信息
     * @param id 教师ID
     * @return 教师对象
     */
    public Teacher getTeacherById(int id) {
        String sql = "SELECT t.id, t.name, t.college_id, t.phone, t.password, t.role, c.college_name " +
                     "FROM teacher t LEFT JOIN college c ON t.college_id = c.id " +
                     "WHERE t.id = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Teacher teacher = new Teacher();
                    teacher.setId(rs.getInt("id"));
                    teacher.setName(rs.getString("name"));
                    teacher.setCollegeId(rs.getInt("college_id"));
                    teacher.setPhone(rs.getString("phone"));
                    teacher.setPassword(rs.getString("password"));
                    teacher.setRole(rs.getString("role"));
                    teacher.setCollegeName(rs.getString("college_name"));
                    return teacher;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting teacher by id: " + e.getMessage());
        }
        
        return null;
    }
}

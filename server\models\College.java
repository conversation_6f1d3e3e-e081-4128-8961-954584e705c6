package server.models;

import java.io.Serializable;

/**
 * 学院实体类
 */
public class College implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private int id;
    private String collegeName;
    private String collegeCode;
    
    public College() {}
    
    public College(String collegeName, String collegeCode) {
        this.collegeName = collegeName;
        this.collegeCode = collegeCode;
    }
    
    public College(int id, String collegeName, String collegeCode) {
        this.id = id;
        this.collegeName = collegeName;
        this.collegeCode = collegeCode;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getCollegeName() {
        return collegeName;
    }
    
    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }
    
    public String getCollegeCode() {
        return collegeCode;
    }
    
    public void setCollegeCode(String collegeCode) {
        this.collegeCode = collegeCode;
    }
    
    @Override
    public String toString() {
        return "College{" +
                "id=" + id +
                ", collegeName='" + collegeName + '\'' +
                ", collegeCode='" + collegeCode + '\'' +
                '}';
    }
}

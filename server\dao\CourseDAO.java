package server.dao;

import server.DatabaseConnection;
import server.models.Course;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 课程数据访问对象
 */
public class CourseDAO {
    
    /**
     * 添加课程
     * @param course 课程对象
     * @return 是否添加成功
     */
    public boolean addCourse(Course course) {
        String sql = "INSERT INTO course (course_name, teacher_id, exam_time, status) VALUES (?, ?, ?, ?)";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, course.getCourseName());
            stmt.setInt(2, course.getTeacherId());
            stmt.setTimestamp(3, course.getExamTime());
            stmt.setString(4, course.getStatus());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error adding course: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取所有课程信息
     * @return 课程列表
     */
    public List<Course> getAllCourses() {
        List<Course> courses = new ArrayList<>();
        String sql = "SELECT c.id, c.course_name, c.teacher_id, c.exam_time, c.status, t.name as teacher_name " +
                     "FROM course c LEFT JOIN teacher t ON c.teacher_id = t.id ORDER BY c.id";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                Course course = new Course();
                course.setId(rs.getInt("id"));
                course.setCourseName(rs.getString("course_name"));
                course.setTeacherId(rs.getInt("teacher_id"));
                course.setExamTime(rs.getTimestamp("exam_time"));
                course.setStatus(rs.getString("status"));
                course.setTeacherName(rs.getString("teacher_name"));
                courses.add(course);
            }
        } catch (SQLException e) {
            System.err.println("Error getting all courses: " + e.getMessage());
        }
        
        return courses;
    }
    
    /**
     * 根据教师ID获取课程列表
     * @param teacherId 教师ID
     * @return 课程列表
     */
    public List<Course> getCoursesByTeacherId(int teacherId) {
        List<Course> courses = new ArrayList<>();
        String sql = "SELECT c.id, c.course_name, c.teacher_id, c.exam_time, c.status, t.name as teacher_name " +
                     "FROM course c LEFT JOIN teacher t ON c.teacher_id = t.id " +
                     "WHERE c.teacher_id = ? AND c.status = 'active' ORDER BY c.id";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, teacherId);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Course course = new Course();
                    course.setId(rs.getInt("id"));
                    course.setCourseName(rs.getString("course_name"));
                    course.setTeacherId(rs.getInt("teacher_id"));
                    course.setExamTime(rs.getTimestamp("exam_time"));
                    course.setStatus(rs.getString("status"));
                    course.setTeacherName(rs.getString("teacher_name"));
                    courses.add(course);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting courses by teacher id: " + e.getMessage());
        }
        
        return courses;
    }
    
    /**
     * 根据ID获取课程信息
     * @param id 课程ID
     * @return 课程对象
     */
    public Course getCourseById(int id) {
        String sql = "SELECT c.id, c.course_name, c.teacher_id, c.exam_time, c.status, t.name as teacher_name " +
                     "FROM course c LEFT JOIN teacher t ON c.teacher_id = t.id WHERE c.id = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Course course = new Course();
                    course.setId(rs.getInt("id"));
                    course.setCourseName(rs.getString("course_name"));
                    course.setTeacherId(rs.getInt("teacher_id"));
                    course.setExamTime(rs.getTimestamp("exam_time"));
                    course.setStatus(rs.getString("status"));
                    course.setTeacherName(rs.getString("teacher_name"));
                    return course;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting course by id: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 更新课程信息
     * @param course 课程对象
     * @return 是否更新成功
     */
    public boolean updateCourse(Course course) {
        String sql = "UPDATE course SET course_name = ?, teacher_id = ?, exam_time = ? WHERE id = ? AND status = 'draft'";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, course.getCourseName());
            stmt.setInt(2, course.getTeacherId());
            stmt.setTimestamp(3, course.getExamTime());
            stmt.setInt(4, course.getId());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error updating course: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 删除课程
     * @param id 课程ID
     * @return 是否删除成功
     */
    public boolean deleteCourse(int id) {
        String sql = "DELETE FROM course WHERE id = ? AND status = 'draft'";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error deleting course: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 设置课程为开课状态
     * @param id 课程ID
     * @return 是否设置成功
     */
    public boolean activateCourse(int id) {
        String sql = "UPDATE course SET status = 'active' WHERE id = ? AND status = 'draft'";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error activating course: " + e.getMessage());
            return false;
        }
    }
}

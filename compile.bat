@echo off
echo 编译学校试卷登记管理系统...

REM 检查lib目录下的依赖文件
if not exist "lib\mysql-connector-java-8.0.33.jar" (
    echo 错误: 缺少MySQL JDBC驱动 lib\mysql-connector-java-8.0.33.jar
    echo 请从 https://dev.mysql.com/downloads/connector/j/ 下载
    pause
    exit /b 1
)

if not exist "lib\gson-2.10.1.jar" (
    echo 错误: 缺少Gson库 lib\gson-2.10.1.jar
    echo 请从 https://github.com/google/gson/releases 下载
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "out" mkdir out

REM 设置类路径
set CLASSPATH=lib\mysql-connector-java-8.0.33.jar;lib\gson-2.10.1.jar;.

REM 编译所有Java文件
echo 编译通用类...
javac -cp %CLASSPATH% -d out common\*.java

echo 编译服务器端...
javac -cp %CLASSPATH% -d out server\*.java
javac -cp %CLASSPATH% -d out server\models\*.java
javac -cp %CLASSPATH% -d out server\dao\*.java
javac -cp %CLASSPATH% -d out server\service\*.java

echo 编译客户端...
javac -cp %CLASSPATH% -d out client\*.java

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！
    echo 可以使用 run_server.bat 启动服务器
    echo 可以使用 run_client.bat 启动客户端
) else (
    echo 编译失败！请检查错误信息
)

pause

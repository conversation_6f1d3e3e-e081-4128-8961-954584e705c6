package server.models;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 试卷实体类
 */
public class ExamPaper implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private int id;
    private String paperName;
    private String paperCode;
    private int teacherId;
    private int courseId;
    private Timestamp examTime;
    private String fileName;
    private Timestamp createdTime;
    private String teacherName; // 用于显示教师姓名
    private String courseName;  // 用于显示课程名称
    
    public ExamPaper() {}
    
    public ExamPaper(String paperName, String paperCode, int teacherId, int courseId, 
                     Timestamp examTime, String fileName) {
        this.paperName = paperName;
        this.paperCode = paperCode;
        this.teacherId = teacherId;
        this.courseId = courseId;
        this.examTime = examTime;
        this.fileName = fileName;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getPaperName() {
        return paperName;
    }
    
    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }
    
    public String getPaperCode() {
        return paperCode;
    }
    
    public void setPaperCode(String paperCode) {
        this.paperCode = paperCode;
    }
    
    public int getTeacherId() {
        return teacherId;
    }
    
    public void setTeacherId(int teacherId) {
        this.teacherId = teacherId;
    }
    
    public int getCourseId() {
        return courseId;
    }
    
    public void setCourseId(int courseId) {
        this.courseId = courseId;
    }
    
    public Timestamp getExamTime() {
        return examTime;
    }
    
    public void setExamTime(Timestamp examTime) {
        this.examTime = examTime;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public Timestamp getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(Timestamp createdTime) {
        this.createdTime = createdTime;
    }
    
    public String getTeacherName() {
        return teacherName;
    }
    
    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }
    
    @Override
    public String toString() {
        return "ExamPaper{" +
                "id=" + id +
                ", paperName='" + paperName + '\'' +
                ", paperCode='" + paperCode + '\'' +
                ", teacherId=" + teacherId +
                ", courseId=" + courseId +
                ", examTime=" + examTime +
                ", fileName='" + fileName + '\'' +
                ", createdTime=" + createdTime +
                ", teacherName='" + teacherName + '\'' +
                ", courseName='" + courseName + '\'' +
                '}';
    }
}

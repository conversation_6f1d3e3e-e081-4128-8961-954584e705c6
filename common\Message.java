package common;

import java.io.Serializable;

/**
 * 客户端与服务器端通信的消息类
 */
public class Message implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String type;  // 消息类型
    private String data;  // 消息数据
    private boolean success; // 操作是否成功
    private String message; // 返回消息
    
    public Message() {}
    
    public Message(String type, String data) {
        this.type = type;
        this.data = data;
    }
    
    public Message(String type, String data, boolean success, String message) {
        this.type = type;
        this.data = data;
        this.success = success;
        this.message = message;
    }
    
    // Getters and Setters
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getData() {
        return data;
    }
    
    public void setData(String data) {
        this.data = data;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    @Override
    public String toString() {
        return "Message{" +
                "type='" + type + '\'' +
                ", data='" + data + '\'' +
                ", success=" + success +
                ", message='" + message + '\'' +
                '}';
    }
}

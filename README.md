# 学校试卷登记管理系统

基于Java的C/S架构试卷管理系统，使用JDK8 + JDBC + MySQL8 + Socket通信实现。

## 系统功能

### 教师功能
- 教师注册与登录
- 查看个人教学课程
- 录入试卷信息（包括文件上传）
- 查看个人试卷列表

### 管理员功能
- 学院信息管理（增删改查）
- 课程信息管理（增删改查、开课设置）
- 查询所有试卷信息
- 下载试卷文件

## 技术架构

- **服务器端**: JDK8 + JDBC + MySQL8
- **客户端**: Socket通信 + Scanner/Print交互
- **数据库**: MySQL 8.0
- **通信协议**: 基于Socket的对象序列化

## 项目结构

```
├── database/           # 数据库脚本
│   └── schema.sql     # 建表脚本
├── common/            # 通用类
│   └── Message.java   # 通信消息类
├── server/            # 服务器端
│   ├── models/        # 实体类
│   ├── dao/          # 数据访问层
│   ├── service/      # 业务逻辑层
│   ├── DatabaseConnection.java  # 数据库连接
│   └── Server.java   # 服务器主程序
├── client/           # 客户端
│   └── Client.java   # 客户端主程序
├── lib/              # 依赖库目录
├── papers/           # 试卷文件存储目录
├── downloads/        # 下载文件目录
└── out/              # 编译输出目录
```

## 安装和运行

### 1. 环境准备

- JDK 8 或更高版本
- MySQL 8.0 或更高版本
- 下载依赖库到 `lib/` 目录：
  - mysql-connector-java-8.0.33.jar
  - gson-2.10.1.jar

### 2. 数据库设置

1. 启动MySQL服务
2. 执行 `database/schema.sql` 脚本创建数据库和表
3. 修改 `server/DatabaseConnection.java` 中的数据库连接参数（如需要）

### 3. 编译项目

```bash
compile.bat
```

### 4. 运行系统

1. 启动服务器：
```bash
run_server.bat
```

2. 启动客户端：
```bash
run_client.bat
```

## 使用说明

### 初始账户

系统预置了一个管理员账户：
- 手机号：13800000000
- 密码：admin123

### 基本操作流程

1. **教师注册**：新教师通过注册功能创建账户
2. **管理员登录**：使用预置管理员账户登录
3. **学院管理**：管理员添加和管理学院信息
4. **课程管理**：管理员创建课程并分配给教师
5. **开课设置**：管理员将课程设置为开课状态
6. **教师登录**：教师登录查看分配的课程
7. **试卷录入**：教师为课程录入试卷信息
8. **试卷查询**：管理员查询和下载试卷

## 注意事项

1. 确保MySQL服务正常运行
2. 数据库连接参数需要根据实际环境调整
3. 试卷文件会保存在 `papers/` 目录下
4. 下载的文件会保存在 `downloads/` 目录下
5. 手机号必须唯一，格式为11位数字
6. 课程设置为开课状态后不能再修改
7. 试卷编号必须唯一

## 系统特点

- 严格按照需求实现，未添加额外功能
- 使用控制台交互，操作简单直观
- 支持文件上传和下载功能
- 完整的权限控制（教师/管理员）
- 数据完整性约束和验证
- 多客户端并发支持

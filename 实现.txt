技术要求(最简单化实现这个系统 但是要满足如下要求 不要自己扩展 只完成下面有的内容)：
①　项目采用C/S结构，服务器端程序采用jdk8+JDBC+MySQL8实现数据的访问，与客户端程序采用Socket的方式实现数据通信。
②　客户端不用开发图形界面，只需要用Scanner与print的方式实现与用户的交互。

主题：学校试卷登记管理系统	
教师注册与登陆	
1、系统的角色包括两类：教师、管理员
2、在数据库中设计一个教师信息表（id，姓名、学院（外键），手机号，密码）。
3、教师具有注册功能，进入注册界面。输入姓名、所属学院（从学院信息表中选择）、密码、手机号等信息。实现注册功能。
4、手机号需要唯一性的处理。
5、教师登陆功能，登陆后，进入个人中心，可以看到教学课程。

学院信息管理	
1、数据库中设计一个学院信息表（id，学院名称、学院编号）。
2、管理员不需要注册，直接在教师信息表中设定一个角色为管理员的用户。
3、管理员登陆后可以实现学院信息的增删改查。

课程信息管理
1、在数据库中设计一个课程表，包括id，课程名称、任课老师（外键），考试时间。
2、管理员登陆后实现课程信息的增删改查，并能选择课程教学的老师。
3、管理员在完成课程设置后，设定开课状态。课程设置为开课状态后，管理员不能再修改课程信息。

教师录入试卷信息
1、设计一个试卷信息表（id，试卷名称，编号，出题教师（外键），所属课程（外键），考试时间，试卷文件名）。
2、教师登陆后可以看到自己教学的课程。
3、教师选择一门课程后，可以为该门课程录入试卷信息，包括输入考试时间，试卷名称，并能上传试卷（把一个试卷文件从一个文件夹复制到管理系统设定的文件夹，生成一个名称唯一的文件，并把文件名保存到数据表中。）

管理员查询
管理员登陆后能查询每门课的出题老师，考试时间和试卷信息，并能下载试卷（把试卷从系统文件夹复制到指定文件夹）
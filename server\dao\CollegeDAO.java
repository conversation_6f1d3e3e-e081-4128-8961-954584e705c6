package server.dao;

import server.DatabaseConnection;
import server.models.College;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 学院数据访问对象
 */
public class CollegeDAO {
    
    /**
     * 获取所有学院信息
     * @return 学院列表
     */
    public List<College> getAllColleges() {
        List<College> colleges = new ArrayList<>();
        String sql = "SELECT id, college_name, college_code FROM college ORDER BY id";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                College college = new College();
                college.setId(rs.getInt("id"));
                college.setCollegeName(rs.getString("college_name"));
                college.setCollegeCode(rs.getString("college_code"));
                colleges.add(college);
            }
        } catch (SQLException e) {
            System.err.println("Error getting all colleges: " + e.getMessage());
        }
        
        return colleges;
    }
    
    /**
     * 根据ID获取学院信息
     * @param id 学院ID
     * @return 学院对象
     */
    public College getCollegeById(int id) {
        String sql = "SELECT id, college_name, college_code FROM college WHERE id = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    College college = new College();
                    college.setId(rs.getInt("id"));
                    college.setCollegeName(rs.getString("college_name"));
                    college.setCollegeCode(rs.getString("college_code"));
                    return college;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting college by id: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 添加学院
     * @param college 学院对象
     * @return 是否添加成功
     */
    public boolean addCollege(College college) {
        String sql = "INSERT INTO college (college_name, college_code) VALUES (?, ?)";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, college.getCollegeName());
            stmt.setString(2, college.getCollegeCode());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error adding college: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 更新学院信息
     * @param college 学院对象
     * @return 是否更新成功
     */
    public boolean updateCollege(College college) {
        String sql = "UPDATE college SET college_name = ?, college_code = ? WHERE id = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, college.getCollegeName());
            stmt.setString(2, college.getCollegeCode());
            stmt.setInt(3, college.getId());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error updating college: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 删除学院
     * @param id 学院ID
     * @return 是否删除成功
     */
    public boolean deleteCollege(int id) {
        String sql = "DELETE FROM college WHERE id = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error deleting college: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查学院编号是否已存在
     * @param collegeCode 学院编号
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    public boolean isCollegeCodeExists(String collegeCode, int excludeId) {
        String sql = "SELECT COUNT(*) FROM college WHERE college_code = ? AND id != ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, collegeCode);
            stmt.setInt(2, excludeId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error checking college code exists: " + e.getMessage());
        }
        
        return false;
    }
}

package server;

import common.Message;
import server.service.ExamManagementService;

import java.io.*;
import java.net.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 服务器主程序
 */
public class Server {
    private static final int PORT = 8888;
    private ServerSocket serverSocket;
    private ExecutorService threadPool;
    private ExamManagementService examService;
    private boolean running = false;
    
    public Server() {
        this.threadPool = Executors.newFixedThreadPool(10);
        this.examService = new ExamManagementService();
    }
    
    /**
     * 启动服务器
     */
    public void start() {
        try {
            // 测试数据库连接
            if (!DatabaseConnection.testConnection()) {
                System.err.println("数据库连接失败，请检查数据库配置！");
                return;
            }
            
            serverSocket = new ServerSocket(PORT);
            running = true;
            
            System.out.println("=== 学校试卷登记管理系统服务器 ===");
            System.out.println("服务器启动成功，监听端口: " + PORT);
            System.out.println("等待客户端连接...");
            
            while (running) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    System.out.println("客户端连接: " + clientSocket.getInetAddress().getHostAddress());
                    
                    // 为每个客户端创建一个处理线程
                    threadPool.submit(new ClientHandler(clientSocket, examService));
                } catch (IOException e) {
                    if (running) {
                        System.err.println("接受客户端连接时出错: " + e.getMessage());
                    }
                }
            }
        } catch (IOException e) {
            System.err.println("服务器启动失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止服务器
     */
    public void stop() {
        running = false;
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
            threadPool.shutdown();
            System.out.println("服务器已停止");
        } catch (IOException e) {
            System.err.println("停止服务器时出错: " + e.getMessage());
        }
    }
    
    /**
     * 客户端处理器
     */
    private static class ClientHandler implements Runnable {
        private Socket clientSocket;
        private ExamManagementService examService;
        private ObjectInputStream inputStream;
        private ObjectOutputStream outputStream;
        
        public ClientHandler(Socket clientSocket, ExamManagementService examService) {
            this.clientSocket = clientSocket;
            this.examService = examService;
        }
        
        @Override
        public void run() {
            try {
                // 创建输入输出流
                outputStream = new ObjectOutputStream(clientSocket.getOutputStream());
                inputStream = new ObjectInputStream(clientSocket.getInputStream());
                
                System.out.println("客户端 " + clientSocket.getInetAddress().getHostAddress() + " 已连接");
                
                // 处理客户端请求
                while (!clientSocket.isClosed()) {
                    try {
                        // 读取客户端请求
                        Message request = (Message) inputStream.readObject();
                        System.out.println("收到请求: " + request.getType());
                        
                        // 处理请求
                        Message response = examService.processRequest(request);
                        
                        // 发送响应
                        outputStream.writeObject(response);
                        outputStream.flush();
                        
                        System.out.println("响应已发送: " + response.getType() + " - " + response.getMessage());
                        
                    } catch (EOFException e) {
                        // 客户端断开连接
                        break;
                    } catch (ClassNotFoundException e) {
                        System.err.println("接收消息时出错: " + e.getMessage());
                        break;
                    }
                }
                
            } catch (IOException e) {
                System.err.println("处理客户端连接时出错: " + e.getMessage());
            } finally {
                // 关闭连接
                closeConnection();
            }
        }
        
        private void closeConnection() {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
                if (clientSocket != null && !clientSocket.isClosed()) {
                    clientSocket.close();
                }
                System.out.println("客户端连接已关闭");
            } catch (IOException e) {
                System.err.println("关闭连接时出错: " + e.getMessage());
            }
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        Server server = new Server();
        
        // 添加关闭钩子，确保服务器正常关闭
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("\n正在关闭服务器...");
            server.stop();
        }));
        
        // 启动服务器
        server.start();
    }
}
